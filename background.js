// background.js - Service Worker 作为数据中心
class TwitterDataManager {
  constructor() {
    this.db = null;
    this.initDB();
    this.setupMessageListener();
  }

  async initDB() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('TwitterInterceptor', 1);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve(this.db);
      };
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // 创建 cache 表
        if (!db.objectStoreNames.contains('cache')) {
          const cacheStore = db.createObjectStore('cache', { keyPath: 'rest_id' });
          cacheStore.createIndex('created_at', 'created_at', { unique: false });
        }
        
        // 创建 tweets 表
        if (!db.objectStoreNames.contains('tweets')) {
          const tweetsStore = db.createObjectStore('tweets', { keyPath: 'rest_id' });
          tweetsStore.createIndex('created_at', 'created_at', { unique: false });
        }
      };
    });
  }

  async saveTweetToCache(tweetData) {
    if (!this.db) await this.initDB();

    // 检查是否已被点赞或收藏，如果是则直接保存到 tweets 表
    if (tweetData.favorited || tweetData.bookmarked) {
      console.log(`Tweet ${tweetData.rest_id} is favorited or bookmarked, saving to tweets table`);
      return await this.saveTweetToTweets(tweetData);
    }

    // 先检查并管理缓存大小
    await this.manageCacheSize();

    const transaction = this.db.transaction(['cache'], 'readwrite');
    const store = transaction.objectStore('cache');

    return new Promise((resolve, reject) => {
      const request = store.put(tweetData);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async saveTweetToTweets(tweetData) {
    if (!this.db) await this.initDB();

    // 如果推文已被点赞或收藏，先检查是否在缓存中，如果在则从缓存中删除
    if (tweetData.favorited || tweetData.bookmarked) {
      await this.removeFromCacheIfExists(tweetData.rest_id);
    }

    const transaction = this.db.transaction(['tweets'], 'readwrite');
    const store = transaction.objectStore('tweets');

    return new Promise((resolve, reject) => {
      const request = store.put(tweetData);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async removeFromCacheIfExists(restId) {
    if (!this.db) await this.initDB();

    const transaction = this.db.transaction(['cache'], 'readwrite');
    const store = transaction.objectStore('cache');

    return new Promise((resolve, reject) => {
      // 先检查是否存在
      const getRequest = store.get(restId);

      getRequest.onsuccess = () => {
        if (getRequest.result) {
          // 存在则删除
          const deleteRequest = store.delete(restId);
          deleteRequest.onsuccess = () => {
            console.log(`Removed tweet ${restId} from cache (moved to tweets)`);
            resolve(true);
          };
          deleteRequest.onerror = () => reject(deleteRequest.error);
        } else {
          // 不存在，直接返回
          resolve(false);
        }
      };

      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  async manageCacheSize() {
    const CACHE_LIMIT = 600;

    if (!this.db) await this.initDB();

    const transaction = this.db.transaction(['cache'], 'readonly');
    const store = transaction.objectStore('cache');

    return new Promise((resolve, reject) => {
      const countRequest = store.count();

      countRequest.onsuccess = () => {
        const currentCount = countRequest.result;

        if (currentCount >= CACHE_LIMIT) {
          // 获取最早的数据并删除
          const deleteCount = currentCount - CACHE_LIMIT + 1;
          this.deleteOldestCacheEntries(deleteCount)
            .then(() => resolve())
            .catch(reject);
        } else {
          resolve();
        }
      };

      countRequest.onerror = () => reject(countRequest.error);
    });
  }

  async deleteOldestCacheEntries(deleteCount) {
    if (!this.db) await this.initDB();

    const transaction = this.db.transaction(['cache'], 'readwrite');
    const store = transaction.objectStore('cache');
    const index = store.index('created_at');

    return new Promise((resolve, reject) => {
      const request = index.openCursor();
      let deletedCount = 0;

      request.onsuccess = (event) => {
        const cursor = event.target.result;

        if (cursor && deletedCount < deleteCount) {
          const deleteRequest = cursor.delete();
          deleteRequest.onsuccess = () => {
            deletedCount++;
            cursor.continue();
          };
          deleteRequest.onerror = () => reject(deleteRequest.error);
        } else {
          console.log(`Deleted ${deletedCount} oldest cache entries`);
          resolve();
        }
      };

      request.onerror = () => reject(request.error);
    });
  }

  async moveTweetToTweets(restId) {
    if (!this.db) await this.initDB();
    
    // 从 cache 中获取数据
    const cacheTransaction = this.db.transaction(['cache'], 'readonly');
    const cacheStore = cacheTransaction.objectStore('cache');
    
    return new Promise((resolve, reject) => {
      const getRequest = cacheStore.get(restId);
      
      getRequest.onsuccess = () => {
        const tweetData = getRequest.result;
        if (!tweetData) {
          resolve(false);
          return;
        }
        
        // 保存到 tweets 表
        const tweetsTransaction = this.db.transaction(['tweets'], 'readwrite');
        const tweetsStore = tweetsTransaction.objectStore('tweets');
        
        const putRequest = tweetsStore.put(tweetData);
        putRequest.onsuccess = () => {
          // 从 cache 中删除
          const deleteTransaction = this.db.transaction(['cache'], 'readwrite');
          const deleteCacheStore = deleteTransaction.objectStore('cache');
          const deleteRequest = deleteCacheStore.delete(restId);
          
          deleteRequest.onsuccess = () => resolve(true);
          deleteRequest.onerror = () => reject(deleteRequest.error);
        };
        putRequest.onerror = () => reject(putRequest.error);
      };
      
      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  async getAllData(storeName) {
    if (!this.db) await this.initDB();
    
    const transaction = this.db.transaction([storeName], 'readonly');
    const store = transaction.objectStore(storeName);
    
    return new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      switch (message.type) {
        case 'SAVE_TWEET_DATA':
          this.handleSaveTweetData(message.data)
            .then(result => sendResponse({ success: true, result }))
            .catch(error => sendResponse({ success: false, error: error.message }));
          return true; // 异步响应
          
        case 'MOVE_TWEET':
          this.moveTweetToTweets(message.rest_id)
            .then(result => sendResponse({ success: true, result }))
            .catch(error => sendResponse({ success: false, error: error.message }));
          return true;
          
        case 'GET_ALL_DATA':
          this.getAllData(message.storeName)
            .then(result => sendResponse({ success: true, data: result }))
            .catch(error => sendResponse({ success: false, error: error.message }));
          return true;
          
        default:
          sendResponse({ success: false, error: 'Unknown message type' });
      }
    });
  }

  async handleSaveTweetData(rawData) {
    const tweets = this.extractTweetData(rawData);
    const results = [];
    
    for (const tweet of tweets) {
      // 检查核心字段是否都为空
      const isEmptyTweet = !tweet.full_text &&
                           !tweet.user_info.name &&
                           !tweet.user_info.screen_name &&
                           !tweet.user_info.profile_image_url;

      if (isEmptyTweet) {
        console.log('Skipping empty tweet:', tweet.rest_id);
        results.push({ rest_id: tweet.rest_id, success: false, reason: 'Empty tweet data' });
        continue; // 跳过保存这个空推文
      }

      try {
        const result = await this.saveTweetToCache(tweet);
        results.push({ rest_id: tweet.rest_id, success: true });
      } catch (error) {
        results.push({ rest_id: tweet.rest_id, success: false, error: error.message });
      }
    }
    
    return results;
  }

  extractTweetData(responseData) {
    const tweets = [];
    
    try {
      const data = typeof responseData === 'string' ? JSON.parse(responseData) : responseData;
      
      // 递归查找推文数据
      this.findTweetsRecursive(data, tweets);
    } catch (error) {
      console.error('Error parsing tweet data:', error);
    }
    
    return tweets;
  }

  findTweetsRecursive(obj, tweets) {
    if (!obj || typeof obj !== 'object') return;
    
    // 检查是否是推文对象
    if (obj.legacy && obj.rest_id) {
      const tweet = this.parseTweetObject(obj);
      if (tweet) {
        tweets.push(tweet);
      }
    }
    
    // 递归搜索
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        this.findTweetsRecursive(obj[key], tweets);
      }
    }
  }

  parseTweetObject(tweetObj) {
    try {
      const legacy = tweetObj.legacy || {};
      const core = tweetObj.core || {};
      const user_results_result = tweetObj.core?.user_results?.result || {};
      const user_legacy = user_results_result.legacy || {};
      const user_avatar = user_results_result.avatar || {}; // 用户头像信息在 user_results.result.avatar 下

      return {
        rest_id: tweetObj.rest_id, // 修改 tweet_id 为 rest_id
        created_at: legacy.created_at || new Date().toISOString(), // 推文创建时间优先从 legacy.created_at 获取
        full_text: legacy.full_text || '',
        bookmarked: legacy.bookmarked || false,
        favorited: legacy.favorited || false,
        media_url_https: this.extractMediaUrls(legacy.entities?.media || []),
        user_info: {
          name: user_legacy.name || user_results_result.core?.name || '', // 用户名
          screen_name: user_legacy.screen_name || user_results_result.core?.screen_name || '', // 用户昵称
          profile_image_url: user_avatar.image_url || user_legacy.profile_image_url_https || '' // 用户头像
        },
        // 原始用户创建时间，如果需要可以取消注释
        // user_created_at: user_legacy.created_at || '',
        saved_at: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error parsing tweet object:', error, tweetObj); // 增加 tweetObj 输出以方便调试
      return null;
    }
  }

  extractMediaUrls(mediaArray) {
    return mediaArray
      .filter(media => media.media_url_https)
      .map(media => media.media_url_https);
  }
}

// 初始化数据管理器
const dataManager = new TwitterDataManager();

console.log('Twitter Data Interceptor Background Script loaded');
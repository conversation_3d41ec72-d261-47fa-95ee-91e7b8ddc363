// options.js - 数据查看页面脚本
class DataViewer {
  constructor() {
    this.currentTab = 'cache';
    this.allData = [];
    this.filteredData = [];
    
    this.initEventListeners();
    this.loadData();
  }

  initEventListeners() {
    // 标签切换
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', (e) => {
        this.switchTab(e.target.dataset.tab);
      });
    });

    // 刷新按钮
    document.getElementById('refreshBtn').addEventListener('click', () => {
      this.loadData();
    });

    // 导出按钮
    document.getElementById('exportBtn').addEventListener('click', () => {
      this.exportData();
    });

    // 搜索框
    const searchBox = document.getElementById('searchBox');
    searchBox.addEventListener('input', (e) => {
      this.filterData(e.target.value);
    });
  }

  switchTab(tabName) {
    // 更新标签样式
    document.querySelectorAll('.tab').forEach(tab => {
      tab.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // 控制导出按钮显示（只在已保存推文页面显示）
    const exportBtn = document.getElementById('exportBtn');
    if (tabName === 'tweets') {
      exportBtn.style.display = 'inline-block';
    } else {
      exportBtn.style.display = 'none';
    }

    // 切换数据
    this.currentTab = tabName;
    this.loadData();
  }

  async loadData() {
    this.showLoading(true);
    
    try {
      const response = await this.sendMessage('GET_ALL_DATA', { 
        storeName: this.currentTab 
      });
      
      if (response.success) {
        this.allData = response.data || [];
        this.filteredData = [...this.allData];
        this.renderTable();
        this.updateStats();
      } else {
        throw new Error(response.error || '加载数据失败');
      }
    } catch (error) {
      console.error('Error loading data:', error);
      this.showError('加载数据失败: ' + error.message);
    } finally {
      this.showLoading(false);
    }
  }

  filterData(searchTerm) {
    if (!searchTerm.trim()) {
      this.filteredData = [...this.allData];
    } else {
      const term = searchTerm.toLowerCase();
      this.filteredData = this.allData.filter(item => 
        item.full_text.toLowerCase().includes(term) ||
        item.user_info.name.toLowerCase().includes(term) ||
        item.user_info.screen_name.toLowerCase().includes(term)
      );
    }
    
    this.renderTable();
    this.updateStats();
  }

  renderTable() {
    const tableBody = document.getElementById('tableBody');
    const table = document.getElementById('dataTable');
    const emptyState = document.getElementById('emptyState');
    
    if (this.filteredData.length === 0) {
      table.style.display = 'none';
      emptyState.style.display = 'block';
      return;
    }
    
    table.style.display = 'table';
    emptyState.style.display = 'none';
    
    tableBody.innerHTML = ''; // 清空表格内容
    this.filteredData.forEach(item => {
      const row = this.createTableRowElement(item);
      tableBody.appendChild(row);
    });
  }

  createTableRowElement(item) {
    const userInfo = item.user_info || {};
    const statusBadgeHtml = this.getStatusBadge(item);
    
    const tr = document.createElement('tr');

    // User Info Cell
    const tdUser = document.createElement('td');
    const userInfoDiv = document.createElement('div');
    userInfoDiv.className = 'user-info';
    
    const avatarImg = document.createElement('img');
    avatarImg.src = userInfo.profile_image_url || 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><circle cx="16" cy="16" r="16" fill="%23ddd"/><text x="16" y="20" text-anchor="middle" fill="white" font-size="12">?</text></svg>';
    avatarImg.alt = 'Avatar';
    avatarImg.className = 'user-avatar';
    avatarImg.onerror = () => {
      avatarImg.src = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><circle cx="16" cy="16" r="16" fill="%23ddd"/><text x="16" y="20" text-anchor="middle" fill="white" font-size="12">?</text></svg>';
    };
    
    const userDetailsDiv = document.createElement('div');
    userDetailsDiv.className = 'user-details';
    const userNameDiv = document.createElement('div');
    userNameDiv.className = 'user-name';
    userNameDiv.textContent = this.escapeHtml(userInfo.name || 'Unknown');
    const userScreenNameDiv = document.createElement('div');
    userScreenNameDiv.className = 'user-screen-name';
    userScreenNameDiv.textContent = `@${this.escapeHtml(userInfo.screen_name || 'unknown')}`;
    
    userDetailsDiv.appendChild(userNameDiv);
    userDetailsDiv.appendChild(userScreenNameDiv);
    userInfoDiv.appendChild(avatarImg);
    userInfoDiv.appendChild(userDetailsDiv);
    tdUser.appendChild(userInfoDiv);
    tr.appendChild(tdUser);

    // Tweet Text Cell
    const tdTweetText = document.createElement('td');
    const tweetTextDiv = document.createElement('div');
    tweetTextDiv.className = 'tweet-text';
    tweetTextDiv.title = this.escapeHtml(item.full_text || '');
    tweetTextDiv.textContent = this.escapeHtml(item.full_text || '');
    tdTweetText.appendChild(tweetTextDiv);
    tr.appendChild(tdTweetText);

    // Status Cell
    const tdStatus = document.createElement('td');
    tdStatus.innerHTML = statusBadgeHtml;
    tr.appendChild(tdStatus);

    // Media Cell
    const tdMedia = document.createElement('td');
    const mediaPreviewContainer = this.createMediaPreviewElement(item.media_url_https || []);
    tdMedia.appendChild(mediaPreviewContainer);
    tr.appendChild(tdMedia);
    
    // Created At Cell
    const tdCreatedAt = document.createElement('td');
    tdCreatedAt.textContent = this.formatDate(item.created_at);
    tr.appendChild(tdCreatedAt);

    // Saved At Cell
    const tdSavedAt = document.createElement('td');
    tdSavedAt.textContent = this.formatDate(item.saved_at);
    tr.appendChild(tdSavedAt);

    return tr;
  }

  getStatusBadge(item) {
    const badges = [];
    
    if (item.favorited) {
      badges.push('<span class="status-badge status-liked">已点赞</span>');
    }
    
    if (item.bookmarked) {
      badges.push('<span class="status-badge status-bookmarked">已收藏</span>');
    }
    
    if (badges.length === 0) {
      badges.push('<span class="status-badge status-normal">普通</span>');
    }
    
    return badges.join(' ');
  }

  createMediaPreviewElement(mediaUrls) {
    const container = document.createElement('div');
    container.className = 'media-preview';

    if (!Array.isArray(mediaUrls) || mediaUrls.length === 0) {
      const noMediaSpan = document.createElement('span');
      noMediaSpan.style.color = '#657786';
      noMediaSpan.textContent = '无媒体';
      container.appendChild(noMediaSpan);
      return container;
    }
    
    mediaUrls.slice(0, 3).forEach(url => {
      const img = document.createElement('img');
      img.src = url;
      img.alt = 'Media';
      img.className = 'media-item';
      img.addEventListener('click', () => {
        window.open(url, '_blank');
      });
      container.appendChild(img);
    });

    if (mediaUrls.length > 3) {
      const moreSpan = document.createElement('span');
      moreSpan.style.fontSize = '12px';
      moreSpan.style.color = '#657786';
      moreSpan.textContent = `+${mediaUrls.length - 3}`;
      container.appendChild(moreSpan);
    }
    
    return container;
  }

  formatDate(dateString) {
    if (!dateString) return '-';
    
    try {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return dateString;
    }
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  updateStats() {
    document.getElementById('totalCount').textContent = this.allData.length;
    document.getElementById('displayCount').textContent = this.filteredData.length;
  }

  showLoading(visible) {
    const loading = document.getElementById('loading');
    const table = document.getElementById('dataTable');
    const emptyState = document.getElementById('emptyState');
    
    if (visible) {
      loading.style.display = 'block';
      table.style.display = 'none';
      emptyState.style.display = 'none';
    } else {
      loading.style.display = 'none';
    }
  }

  showError(message) {
    const emptyState = document.getElementById('emptyState');
    emptyState.innerHTML = `
      <h3>加载失败</h3>
      <p>${message}</p>
    `;
    emptyState.style.display = 'block';
    document.getElementById('dataTable').style.display = 'none';
  }

  exportData() {
    // 只在已保存推文页面允许导出
    if (this.currentTab !== 'tweets') {
      alert('导出功能仅在"已保存推文"页面可用');
      return;
    }

    // 检查是否有数据可导出
    if (this.filteredData.length === 0) {
      alert('没有数据可导出');
      return;
    }

    try {
      // 准备导出数据，清理和格式化
      const exportData = this.prepareExportData(this.filteredData);

      // 创建JSON字符串
      const jsonString = JSON.stringify(exportData, null, 2);

      // 创建下载链接
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      // 生成文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
      const searchTerm = document.getElementById('searchBox').value.trim();
      const filenameSuffix = searchTerm ? `_搜索_${searchTerm}` : '_全部';
      const filename = `推文数据_${timestamp}${filenameSuffix}.json`;

      // 创建下载链接并触发下载
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      // 清理URL对象
      URL.revokeObjectURL(url);

      // 显示成功消息
      alert(`成功导出 ${this.filteredData.length} 条数据到文件: ${filename}`);

    } catch (error) {
      console.error('导出数据时出错:', error);
      alert('导出数据失败: ' + error.message);
    }
  }

  prepareExportData(data) {
    return {
      export_info: {
        export_time: new Date().toISOString(),
        total_count: data.length,
        data_source: '已保存推文',
        search_term: document.getElementById('searchBox').value.trim() || null
      },
      tweets: data.map(tweet => ({
        rest_id: tweet.rest_id,
        created_at: tweet.created_at,
        saved_at: tweet.saved_at,
        full_text: tweet.full_text,
        user_info: {
          name: tweet.user_info?.name || '',
          screen_name: tweet.user_info?.screen_name || '',
          profile_image_url: tweet.user_info?.profile_image_url || ''
        },
        status: {
          favorited: tweet.favorited || false,
          bookmarked: tweet.bookmarked || false
        },
        media_url_https: tweet.media_url_https || []
      }))
    };
  }

  sendMessage(type, data = {}) {
    return new Promise((resolve, reject) => {
      const message = { type, ...data };

      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }

        resolve(response);
      });
    });
  }
}

// 初始化数据查看器
document.addEventListener('DOMContentLoaded', () => {
  new DataViewer();
});
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Twitter Data Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .header h1 {
            color: #1DA1F2;
            margin-bottom: 10px;
        }
        
        .tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 12px 24px;
            background: white;
            border: 2px solid #e1e8ed;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .tab.active {
            background: #1DA1F2;
            color: white;
            border-color: #1DA1F2;
        }
        
        .tab:hover:not(.active) {
            border-color: #1DA1F2;
        }
        
        .tab-content {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table-container {
            overflow-x: auto;
            max-height: 70vh;
            overflow-y: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        thead {
            background: #f8f9fa;
            position: sticky;
            top: 0;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e1e8ed;
        }
        
        th {
            font-weight: 600;
            color: #333;
        }
        
        tr:hover {
            background-color: #f8f9fa;
        }
        
        .tweet-text {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
        }
        
        .user-details {
            display: flex;
            flex-direction: column;
        }
        
        .user-name {
            font-weight: 600;
            font-size: 14px;
        }
        
        .user-screen-name {
            color: #657786;
            font-size: 12px;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-liked {
            background: #ffebee;
            color: #e91e63;
        }
        
        .status-bookmarked {
            background: #e3f2fd;
            color: #2196f3;
        }
        
        .status-normal {
            background: #f5f5f5;
            color: #666;
        }
        
        .media-preview {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }
        
        .media-item {
            width: 40px;
            height: 40px;
            border-radius: 4px;
            object-fit: cover;
            cursor: pointer;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1DA1F2;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #657786;
        }
        
        .empty-state h3 {
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        .controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e1e8ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .search-box {
            padding: 8px 12px;
            border: 1px solid #e1e8ed;
            border-radius: 20px;
            outline: none;
            width: 250px;
        }
        
        .search-box:focus {
            border-color: #1DA1F2;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        
        .btn-refresh {
            background-color: #1DA1F2;
            color: white;
        }
        
        .btn-refresh:hover {
            background-color: #1991db;
        }

        .btn-export {
            background-color: #17bf63;
            color: white;
            margin-left: 8px;
        }

        .btn-export:hover {
            background-color: #14a456;
        }

        .btn-export:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .stats-bar {
            display: flex;
            gap: 20px;
            align-items: center;
            font-size: 14px;
            color: #657786;
        }

        /* 视图切换按钮样式 */
        .view-toggle {
            display: flex;
            gap: 5px;
            align-items: center;
        }

        .view-btn {
            padding: 6px 12px;
            border: 1px solid #e1e8ed;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .view-btn.active {
            background: #1DA1F2;
            color: white;
            border-color: #1DA1F2;
        }

        .view-btn:hover:not(.active) {
            border-color: #1DA1F2;
            background: #f8f9fa;
        }

        /* 卡片布局样式 */
        .cards-container {
            display: none;
            padding: 20px;
            column-count: auto;
            column-width: 350px;
            column-gap: 20px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .cards-container.active {
            display: block;
        }

        .tweet-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            break-inside: avoid;
            transition: all 0.3s ease;
            border: 1px solid #e1e8ed;
            overflow: hidden;
        }

        .tweet-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            border-color: #1DA1F2;
        }

        .card-header {
            padding: 16px 16px 12px 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .card-user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .card-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
        }

        .card-user-details {
            flex: 1;
            min-width: 0;
        }

        .card-user-name {
            font-weight: 600;
            font-size: 15px;
            color: #14171a;
            margin-bottom: 2px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .card-user-screen-name {
            color: #657786;
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .card-content {
            padding: 16px;
        }

        .card-tweet-text {
            font-size: 15px;
            line-height: 1.5;
            color: #14171a;
            margin-bottom: 12px;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .card-media {
            margin-top: 12px;
        }

        .card-media-grid {
            display: grid;
            gap: 8px;
            border-radius: 8px;
            overflow: hidden;
        }

        .card-media-grid.single {
            grid-template-columns: 1fr;
        }

        .card-media-grid.double {
            grid-template-columns: 1fr 1fr;
        }

        .card-media-grid.triple {
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
        }

        .card-media-grid.quad {
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
        }

        .card-media-item {
            width: 100%;
            height: auto;
            min-height: 120px;
            object-fit: cover;
            cursor: pointer;
            transition: transform 0.2s;
            border-radius: 4px;
        }

        .card-media-item:hover {
            transform: scale(1.02);
        }

        .card-media-grid.triple .card-media-item:first-child {
            grid-row: 1 / 3;
        }

        .card-footer {
            padding: 12px 16px;
            border-top: 1px solid #f0f0f0;
            background: #fafafa;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 13px;
        }

        .card-status {
            display: flex;
            gap: 8px;
        }

        .card-status-badge {
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 500;
        }

        .card-status-liked {
            background: #ffebee;
            color: #e91e63;
        }

        .card-status-bookmarked {
            background: #e3f2fd;
            color: #2196f3;
        }

        .card-status-normal {
            background: #f5f5f5;
            color: #666;
        }

        .card-dates {
            color: #657786;
            font-size: 12px;
        }

        .card-date {
            margin-bottom: 2px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .cards-container {
                column-width: 100%;
                column-count: 1;
                padding: 10px;
            }

            .tweet-card {
                margin-bottom: 15px;
            }
        }

        @media (min-width: 1400px) {
            .cards-container {
                column-width: 320px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Twitter Data Viewer</h1>
            <p>查看拦截到的Twitter数据</p>
        </div>
        
        <div class="tabs">
            <div class="tab active" data-tab="cache">缓存数据</div>
            <div class="tab" data-tab="tweets">已保存推文</div>
        </div>
        
        <div class="tab-content">
            <div class="controls">
                <div class="stats-bar">
                    <span>总计: <strong id="totalCount">0</strong> 条数据</span>
                    <span>显示: <strong id="displayCount">0</strong> 条</span>
                    <div class="view-toggle" id="viewToggle" style="display: none;">
                        <span style="font-size: 12px; color: #657786;">视图:</span>
                        <button class="view-btn active" data-view="table">
                            <span>📋</span> 表格
                        </button>
                        <button class="view-btn" data-view="cards">
                            <span>🎴</span> 卡片
                        </button>
                    </div>
                </div>
                <div>
                    <input type="text" class="search-box" id="searchBox" placeholder="搜索推文内容或用户...">
                    <button class="btn btn-refresh" id="refreshBtn">刷新</button>
                    <button class="btn btn-export" id="exportBtn" style="display: none;">导出数据</button>
                </div>
            </div>
            
            <div class="table-container">
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>加载数据中...</p>
                </div>
                
                <div class="empty-state" id="emptyState" style="display: none;">
                    <h3>暂无数据</h3>
                    <p>尚未拦截到任何推文数据</p>
                </div>
                
                <table id="dataTable" style="display: none;">
                    <thead>
                        <tr>
                            <th>用户</th>
                            <th>推文内容</th>
                            <th>状态</th>
                            <th>媒体</th>
                            <th>发布时间</th>
                            <th>保存时间</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                    </tbody>
                </table>
            </div>

            <!-- 卡片视图容器 -->
            <div class="cards-container" id="cardsContainer">
                <!-- 卡片将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script src="options.js"></script>
</body>
</html>
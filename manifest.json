{"manifest_version": 3, "name": "Twitter Data Interceptor", "version": "1.0.0", "description": "拦截并保存Twitter GraphQL请求数据", "permissions": ["storage", "activeTab", "declarativeNetRequest"], "host_permissions": ["*://x.com/*", "*://twitter.com/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["*://x.com/*", "*://twitter.com/*"], "js": ["content.js"], "run_at": "document_start"}], "action": {"default_popup": "popup.html", "default_title": "Twitter Data Interceptor"}, "options_page": "options.html", "web_accessible_resources": [{"resources": ["injected.js"], "matches": ["*://x.com/*", "*://twitter.com/*"]}], "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self';"}}
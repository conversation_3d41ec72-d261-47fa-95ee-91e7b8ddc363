# Twitter Data Interceptor

一个Chrome扩展，用于拦截和保存Twitter GraphQL请求数据。

## 功能特性

- ✅ 双重拦截：同时拦截 fetch 和 XMLHttpRequest 请求
- ✅ 精准识别：拦截包含特定端点的GraphQL请求
- ✅ 数据筛选：提取推文ID、用户信息、媒体URL等关键数据
- ✅ 智能分类：缓存数据和已保存推文分别存储
- ✅ 数据查看：表格形式查看两个数据库中的数据
- ✅ 模块化架构：清晰的代码结构，易于维护

## 安装方法

### 开发者模式安装

1. 下载源代码到本地文件夹
2. 打开Chrome浏览器，进入 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含源代码的文件夹
6. 扩展安装完成

## 文件结构

```
twitter-interceptor/
├── manifest.json          # 扩展配置文件
├── background.js          # 后台脚本（数据中心）
├── content.js            # 内容脚本
├── injected.js           # 注入脚本（网络拦截）
├── popup.html            # 弹出页面HTML
├── popup.js              # 弹出页面脚本
├── options.html          # 数据查看页面HTML
├── options.js            # 数据查看页面脚本
└── README.md             # 说明文档
```

## 使用方法

### 1. 基本使用

1. 安装扩展后，访问 [Twitter/X](https://x.com)
2. 正常浏览推文，扩展会自动拦截GraphQL请求
3. 点击扩展图标查看统计信息

### 2. 查看数据

- 点击扩展弹出窗口中的"查看数据"按钮
- 或直接访问 `chrome-extension://[扩展ID]/options.html`
- 在数据查看页面可以：
  - 切换查看"缓存数据"和"已保存推文"
  - 搜索特定用户或推文内容
  - 查看详细的推文信息和媒体

### 3. 数据流转

1. **自动缓存**：浏览Twitter时自动拦截并缓存推文数据
2. **手动保存**：点击推文的"喜欢"或"书签"按钮
3. **数据转移**：被操作的推文从缓存移动到已保存数据库

## 拦截的端点

扩展会拦截以下GraphQL端点：

- `TweetDetail` - 推文详情
- `ModeratedTimeline` - 审核时间线
- `UserTweets` - 用户推文
- `SearchTimeline` - 搜索时间线
- `HomeTimeline` - 主页时间线
- `HomeLatestTimeline` - 主页最新时间线
- `Bookmarks` - 书签
- `Likes` - 点赞
- `ListLatestTweetsTimeline` - 列表时间线
- `UserMedia` - 用户媒体

## 提取的数据字段

每条推文数据包含：

- `tweet_id` - 推文ID
- `created_at` - 创建时间
- `full_text` - 推文文本
- `bookmarked` - 是否已收藏
- `favorited` - 是否已点赞
- `media_url_https` - 媒体URL数组
- `user_info` - 用户信息
  - `name` - 用户名
  - `screen_name` - 用户账号
  - `profile_image_url` - 头像URL
- `saved_at` - 保存时间

## 架构说明

### 权限设计
- 使用Background Script作为数据中心
- 通过消息传递机制实现组件间通信
- IndexedDB存储在扩展环境中，确保数据安全

### 拦截机制
1. **injected.js** - 注入到页面环境，拦截网络请求
2. **content.js** - 内容脚本，处理页面与扩展的通信
3. **background.js** - 后台脚本，管理数据存储

## 注意事项

- 扩展仅在Twitter/X网站上激活
- 数据存储在本地，不会上传到任何服务器
- 清除浏览器数据时会同时清除扩展数据
- 扩展更新时数据会保留

## 故障排除

### 扩展无法正常工作
1. 检查是否在Twitter/X网站上
2. 刷新页面重新加载扩展
3. 检查浏览器控制台是否有错误信息

### 数据无法保存
1. 检查IndexedDB是否被禁用
2. 确认有足够的存储空间
3. 重启浏览器并重新加载扩展

### 统计数据不准确
1. 点击"刷新统计"按钮
2. 检查网络连接是否正常
3. 确认Twitter页面完全加载

## 开发说明

这是一个最小可行性产品(MVP)，包含核心功能。未来可以扩展的功能：

- 数据导出功能
- 更多筛选和排序选项
- 推文内容分析
- 数据备份和恢复
- 自定义拦截规则

## 许可证

本项目仅供学习和研究使用，请遵守Twitter的服务条款和相关法律法规。
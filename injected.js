// injected.js - 注入到页面环境的脚本，拦截网络请求
(function() {
  'use strict';
  
  class NetworkInterceptor {
    constructor() {
      this.originalFetch = window.fetch;
      this.originalXHROpen = XMLHttpRequest.prototype.open;
      this.originalXHRSend = XMLHttpRequest.prototype.send;
      
      this.setupFetchInterceptor();
      this.setupXHRInterceptor();
    }

    setupFetchInterceptor() {
      const self = this;
      
      window.fetch = function(url, options = {}) {
        return self.originalFetch.apply(this, arguments)
          .then(response => {
            // 克隆响应以避免消费
            const clonedResponse = response.clone();
            
            if (self.shouldInterceptUrl(url)) {
              clonedResponse.text().then(text => {
                self.handleInterceptedResponse(url, text, options);
              }).catch(error => {
                console.error('Error reading fetch response:', error);
              });
            }
            
            return response;
          });
      };
    }

    setupXHRInterceptor() {
      const self = this;
      
      XMLHttpRequest.prototype.open = function(method, url, ...args) {
        this._interceptedUrl = url;
        this._interceptedMethod = method;
        return self.originalXHROpen.apply(this, arguments);
      };
      
      XMLHttpRequest.prototype.send = function(data) {
        const xhr = this;
        
        // 保存原始的 onreadystatechange
        const originalOnReadyStateChange = xhr.onreadystatechange;
        
        xhr.onreadystatechange = function() {
          if (xhr.readyState === 4 && xhr.status === 200) {
            if (self.shouldInterceptUrl(xhr._interceptedUrl)) {
              self.handleInterceptedResponse(xhr._interceptedUrl, xhr.responseText, {
                method: xhr._interceptedMethod,
                body: data
              });
            }
          }
          
          // 调用原始处理器
          if (originalOnReadyStateChange) {
            originalOnReadyStateChange.apply(this, arguments);
          }
        };
        
        return self.originalXHRSend.apply(this, arguments);
      };
    }

    shouldInterceptUrl(url) {
      if (!url || typeof url !== 'string') return false;
      
      return url.includes('/i/api/graphql/') && (
        url.includes('TweetDetail') ||
        url.includes('ModeratedTimeline') ||
        url.includes('UserTweets') ||
        url.includes('SearchTimeline') ||
        url.includes('HomeTimeline') ||
        url.includes('HomeLatestTimeline') ||
        url.includes('Bookmarks') ||
        url.includes('Likes') ||
        url.includes('ListLatestTweetsTimeline') ||
        url.includes('UserMedia') ||
        url.includes('FavoriteTweet') ||
        url.includes('CreateBookmark')
      );
    }

    handleInterceptedResponse(url, responseText, options) {
      try {
        // 检查是否是动作请求
        if (url.includes('FavoriteTweet') || url.includes('CreateBookmark')) {
          this.sendActionMessage(url, options.body);
        } else {
          // 普通GraphQL响应
          this.sendResponseMessage(url, responseText);
        }
      } catch (error) {
        console.error('Error handling intercepted response:', error);
      }
    }

    sendResponseMessage(url, response) {
      window.postMessage({
        type: 'TWITTER_API_RESPONSE',
        payload: {
          url: url,
          response: response,
          timestamp: Date.now()
        }
      }, '*');
    }

    sendActionMessage(url, requestData) {
      window.postMessage({
        type: 'TWITTER_ACTION_REQUEST',
        payload: {
          url: url,
          requestData: requestData,
          timestamp: Date.now()
        }
      }, '*');
    }
  }

  // 初始化拦截器
  const interceptor = new NetworkInterceptor();
  
  console.log('Twitter Network Interceptor injected and active');
  
})();
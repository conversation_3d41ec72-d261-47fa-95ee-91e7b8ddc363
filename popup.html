<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Twitter Data Interceptor</title>
    <style>
        body {
            width: 350px;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            color: #1DA1F2;
        }
        
        .stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            flex: 1;
            margin: 0 5px;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1DA1F2;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .btn {
            padding: 12px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        
        .btn-primary {
            background-color: #1DA1F2;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #1991db;
        }
        
        .btn-secondary {
            background-color: #657786;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #5a6c79;
        }
        
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 6px;
            font-size: 13px;
            text-align: center;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin-top: 10px;
        }
        
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #1DA1F2;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Twitter Data Interceptor</h1>
    </div>
    
    <div class="stats">
        <div class="stat-item">
            <div class="stat-number" id="cacheCount">-</div>
            <div class="stat-label">缓存推文</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="savedCount">-</div>
            <div class="stat-label">已保存推文</div>
        </div>
    </div>
    
    <div class="actions">
        <button class="btn btn-primary" id="refreshStats">刷新统计</button>
        <button class="btn btn-primary" id="viewData">查看数据</button>
        <button class="btn btn-secondary" id="openOptions">打开选项</button>
    </div>
    
    <div class="loading" id="loading">
        <div class="spinner"></div>
        <div>加载中...</div>
    </div>
    
    <div class="status" id="status" style="display: none;"></div>
    
    <script src="popup.js"></script>
</body>
</html>
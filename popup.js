// popup.js - 弹出页面脚本
class PopupController {
  constructor() {
    this.initEventListeners();
    this.loadStats();
  }

  initEventListeners() {
    document.getElementById('refreshStats').addEventListener('click', () => {
      this.loadStats();
    });

    document.getElementById('viewData').addEventListener('click', () => {
      chrome.tabs.create({ url: chrome.runtime.getURL('options.html') });
    });

    document.getElementById('openOptions').addEventListener('click', () => {
      chrome.runtime.openOptionsPage();
    });
  }

  async loadStats() {
    this.showLoading(true);
    
    try {
      // 获取缓存数据统计
      const cacheResult = await this.sendMessage('GET_ALL_DATA', { storeName: 'cache' });
      const cacheCount = cacheResult.success ? cacheResult.data.length : 0;
      
      // 获取已保存数据统计
      const savedResult = await this.sendMessage('GET_ALL_DATA', { storeName: 'tweets' });
      const savedCount = savedResult.success ? savedResult.data.length : 0;
      
      // 更新UI
      document.getElementById('cacheCount').textContent = cacheCount;
      document.getElementById('savedCount').textContent = savedCount;
      
      this.showStatus('统计数据已更新', 'success');
      
    } catch (error) {
      console.error('Error loading stats:', error);
      this.showStatus('加载统计数据失败: ' + error.message, 'error');
    } finally {
      this.showLoading(false);
    }
  }

  sendMessage(type, data = {}) {
    return new Promise((resolve, reject) => {
      const message = { type, ...data };
      
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }
        
        resolve(response);
      });
    });
  }

  showLoading(visible) {
    const loading = document.getElementById('loading');
    loading.style.display = visible ? 'block' : 'none';
  }

  showStatus(message, type) {
    const status = document.getElementById('status');
    status.textContent = message;
    status.className = `status ${type}`;
    status.style.display = 'block';
    
    // 3秒后自动隐藏
    setTimeout(() => {
      status.style.display = 'none';
    }, 3000);
  }
}

// 初始化弹出页面控制器
document.addEventListener('DOMContentLoaded', () => {
  new PopupController();
});
// content.js - 内容脚本，负责注入和通信
class TwitterContentScript {
  constructor() {
    this.targetEndpoints = [
      'TweetDetail',
      'ModeratedTimeline', 
      'UserTweets',
      'SearchTimeline',
      'HomeTimeline',
      'HomeLatestTimeline',
      'Bookmarks',
      'Likes',
      'ListLatestTweetsTimeline',
      'UserMedia'
    ];
    
    this.actionEndpoints = [
      'FavoriteTweet',
      'CreateBookmark'
    ];
    
    this.injectScript();
    this.setupMessageListener();
  }

  injectScript() {
    // 注入脚本到页面环境
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('injected.js');
    script.onload = function() {
      this.remove();
    };
    (document.head || document.documentElement).appendChild(script);
  }

  setupMessageListener() {
    // 监听来自注入脚本的消息
    window.addEventListener('message', (event) => {
      if (event.source !== window || !event.data.type) return;
      
      switch (event.data.type) {
        case 'TWITTER_API_RESPONSE':
          this.handleApiResponse(event.data.payload);
          break;
        case 'TWITTER_ACTION_REQUEST':
          this.handleActionRequest(event.data.payload);
          break;
      }
    });
  }

  handleApiResponse(payload) {
    const { url, response } = payload;
    
    // 检查是否是目标端点
    const isTargetEndpoint = this.targetEndpoints.some(endpoint => {
      // 检查 URL 是否以 /endpoint 结尾，或者包含 /endpoint? 或 /endpoint/
      // 这样可以匹配如 /HomeTimeline, /HomeTimeline?vars=..., /HomeTimeline/nested
      const endpointPattern = `/${endpoint}`;
      const urlPath = new URL(url).pathname; // 获取 URL 的路径部分
      return urlPath.endsWith(endpointPattern) || url.includes(`${endpointPattern}?`) || url.includes(`${endpointPattern}/`);
    });
    
    if (isTargetEndpoint) {
      this.sendToBackground('SAVE_TWEET_DATA', response);
    }
  }

  handleActionRequest(payload) {
    const { url, requestData } = payload;
    
    // 检查是否是动作端点
    const isActionEndpoint = this.actionEndpoints.some(endpoint => 
      url.includes(`/${endpoint}`)
    );
    
    if (isActionEndpoint) {
      try {
        const data = JSON.parse(requestData);
        const tweetId = data.variables?.tweet_id;
        
        if (tweetId) {
          this.sendToBackground('MOVE_TWEET', tweetId);
        }
      } catch (error) {
        console.error('Error parsing action request:', error);
      }
    }
  }

  sendToBackground(type, data) {
    const message = { type };
    
    if (type === 'SAVE_TWEET_DATA') {
      message.data = data;
    } else if (type === 'MOVE_TWEET') {
      message.rest_id = data;
    }
    
    chrome.runtime.sendMessage(message, (response) => {
      if (chrome.runtime.lastError) {
        console.error('Error sending message to background:', chrome.runtime.lastError);
        return;
      }
      
      if (response && !response.success) {
        console.error('Background script error:', response.error);
      }
    });
  }
}

// 初始化内容脚本
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new TwitterContentScript();
  });
} else {
  new TwitterContentScript();
}

console.log('Twitter Content Script loaded');